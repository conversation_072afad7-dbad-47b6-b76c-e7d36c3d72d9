> 尽量按此模板PR内容，或粘贴相关的ISSUE链接。

## 已知问题

1. (示例)版本号管理不规范
   - 版本号直接写在环境变量中，容易出错
   - 多处维护版本号，可能不一致

## 解决方案

1. (示例)将版本号管理从 `.env.local` 迁移到 `package.json`
   - 统一从 `package.json` 读取版本号
   - 使用 IIFE 优雅处理版本号获取逻辑
   - 保持向后兼容，支持环境变量覆盖

## 改动收益

1. (示例)更规范的版本管理
   - 统一从 `package.json` 读取
   - 保持与 npm 生态一致
   - 减少人为错误

## 具体改动

1. （示例）`blog.config.js`
   - 移除原有的静态版本号配置
   - 在文件末尾添加动态版本号获取逻辑
   - 保持向后兼容，优先使用环境变量
   - 添加错误处理和默认值

## 测试确认

- [x] 本地开发环境测试通过
- [x] 生产环境构建测试通过
- [x] 版本号正确显示
- [x] 环境变量配置正常工作
