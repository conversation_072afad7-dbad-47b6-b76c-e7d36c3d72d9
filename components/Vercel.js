const Vercel = () => {
  return (
    <a
      href="https://vercel.com?utm_source=<PERSON>ary&utm_campaign=oss"
      target="_blank"
      rel="noreferrer"
      aria-label="Vercel"
    >
      <svg
        width="135"
        height="28"
        viewBox="0 0 135 28"
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
        className="inline-block"
      >
        <g clipPath="url(#clip0)">
          <path
            d="M129.818 0H5.09091C2.27928 0 0 2.27928 0 5.09091V22.9091C0 25.7207 2.27928 28 5.09091 28H129.818C132.63 28 134.909 25.7207 134.909 22.9091V5.09091C134.909 2.27928 132.63 0 129.818 0Z"
            fill="black"
          />
          <path
            d="M38.4601 9.68997V16.8639H39.355V14.3036H41.1597C42.507 14.3036 43.4665 13.354 43.4665 12.0117C43.4665 10.6445 42.5268 9.68997 41.1696 9.68997H38.4601ZM39.355 10.4854H40.9359C41.975 10.4854 42.5467 11.0273 42.5467 12.0117C42.5467 12.9613 41.9551 13.5081 40.9359 13.5081H39.355V10.4854ZM46.6734 16.9584C48.1996 16.9584 49.1442 15.9044 49.1442 14.1843C49.1442 12.4591 48.1996 11.4101 46.6734 11.4101C45.1471 11.4101 44.2025 12.4591 44.2025 14.1843C44.2025 15.9044 45.1471 16.9584 46.6734 16.9584ZM46.6734 16.1878C45.6591 16.1878 45.0874 15.452 45.0874 14.1843C45.0874 12.9116 45.6591 12.1807 46.6734 12.1807C47.6875 12.1807 48.2593 12.9116 48.2593 14.1843C48.2593 15.452 47.6875 16.1878 46.6734 16.1878ZM56.8255 11.5046H55.9654L54.9115 15.7901H54.8319L53.6338 11.5046H52.8135L51.6153 15.7901H51.5358L50.4818 11.5046H49.6167L51.1182 16.8639H51.9832L53.1764 12.7177H53.2559L54.4541 16.8639H55.3241L56.8255 11.5046ZM59.7043 12.1658C60.5544 12.1658 61.1212 12.7922 61.1411 13.7418H58.1879C58.2526 12.7922 58.8492 12.1658 59.7043 12.1658ZM61.1162 15.4769C60.8925 15.9492 60.4252 16.2027 59.7341 16.2027C58.8243 16.2027 58.2327 15.5315 58.1879 14.4726V14.4328H62.0409V14.1047C62.0409 12.4392 61.161 11.4101 59.7142 11.4101C58.2427 11.4101 57.298 12.5038 57.298 14.1892C57.298 15.8845 58.2277 16.9584 59.7142 16.9584C60.8875 16.9584 61.7227 16.3916 61.9714 15.4769H61.1162ZM63.1796 16.8639H64.0346V13.5429C64.0346 12.7873 64.6264 12.2404 65.4416 12.2404C65.6109 12.2404 65.9189 12.2702 65.9889 12.2901V11.435C65.8794 11.42 65.6999 11.4101 65.5612 11.4101C64.8504 11.4101 64.2338 11.778 64.0747 12.3H63.9951V11.5046H63.1796V16.8639ZM68.8423 12.1658C69.6925 12.1658 70.2595 12.7922 70.2792 13.7418H67.3259C67.3908 12.7922 67.9877 12.1658 68.8423 12.1658ZM70.2544 15.4769C70.0304 15.9492 69.5633 16.2027 68.8722 16.2027C67.9622 16.2027 67.371 15.5315 67.3259 14.4726V14.4328H71.179V14.1047C71.179 12.4392 70.2989 11.4101 68.8525 11.4101C67.3806 11.4101 66.4362 12.5038 66.4362 14.1892C66.4362 15.8845 67.3659 16.9584 68.8525 16.9584C70.0259 16.9584 70.8609 16.3916 71.1097 15.4769H70.2544ZM74.3017 16.9584C75.0424 16.9584 75.6788 16.6055 76.0167 16.0088H76.0962V16.8639H76.9114V9.37675H76.0568V12.3497H75.9817C75.6788 11.7631 75.0475 11.4101 74.3017 11.4101C72.9392 11.4101 72.0496 12.5038 72.0496 14.1843C72.0496 15.8696 72.929 16.9584 74.3017 16.9584ZM74.5002 12.1807C75.47 12.1807 76.0765 12.9563 76.0765 14.1843C76.0765 15.4222 75.4745 16.1878 74.5002 16.1878C73.5209 16.1878 72.9341 15.4371 72.9341 14.1843C72.9341 12.9364 73.5259 12.1807 74.5002 12.1807ZM83.7033 16.9584C85.0607 16.9584 85.9503 15.8597 85.9503 14.1843C85.9503 12.4989 85.0651 11.4101 83.7033 11.4101C82.9677 11.4101 82.316 11.773 82.0227 12.3497H81.9431V9.37675H81.0879V16.8639H81.9037V16.0088H81.9832C82.3211 16.6055 82.9575 16.9584 83.7033 16.9584ZM83.5041 12.1807C84.4835 12.1807 85.0651 12.9314 85.0651 14.1843C85.0651 15.4371 84.4835 16.1878 83.5041 16.1878C82.5299 16.1878 81.9234 15.4222 81.9234 14.1843C81.9234 12.9464 82.5299 12.1807 83.5041 12.1807ZM87.3478 18.8029C88.2972 18.8029 88.7249 18.435 89.1818 17.1921L91.2754 11.5046H90.3654L88.8986 15.9144H88.819L87.3478 11.5046H86.4231L88.4067 16.8689L88.3068 17.1871C88.0834 17.8334 87.8149 18.0671 87.3229 18.0671C87.2033 18.0671 87.069 18.0621 86.9647 18.0422V18.773C87.0843 18.7929 87.2332 18.8029 87.3478 18.8029ZM98.4148 16.8639L100.895 9.68997H99.5284L97.7383 15.3675H97.6543L95.8496 9.68997H94.4324L96.9384 16.8639H98.4148ZM103.516 12.295C104.257 12.295 104.744 12.8121 104.769 13.6175H102.208C102.263 12.822 102.78 12.295 103.516 12.295ZM104.778 15.3675C104.6 15.7702 104.177 15.9939 103.565 15.9939C102.755 15.9939 102.233 15.4272 102.203 14.5223V14.4577H106.001V14.06C106.001 12.3447 105.072 11.3156 103.521 11.3156C101.945 11.3156 100.965 12.4144 100.965 14.1644C100.965 15.9144 101.93 16.9733 103.53 16.9733C104.813 16.9733 105.723 16.3568 105.947 15.3675H104.778ZM107.036 16.8639H108.269V13.7219C108.269 12.9613 108.826 12.4641 109.626 12.4641C109.835 12.4641 110.163 12.4989 110.257 12.5337V11.3902C110.143 11.3555 109.924 11.3355 109.745 11.3355C109.045 11.3355 108.458 11.7333 108.308 12.2702H108.224V11.425H107.036V16.8639ZM115.627 13.2546C115.498 12.1111 114.652 11.3156 113.255 11.3156C111.62 11.3156 110.66 12.3647 110.66 14.1296C110.66 15.9193 111.625 16.9733 113.26 16.9733C114.638 16.9733 115.493 16.2077 115.627 15.0692H114.454C114.324 15.636 113.897 15.9392 113.255 15.9392C112.415 15.9392 111.908 15.273 111.908 14.1296C111.908 13.001 112.41 12.3497 113.255 12.3497C113.932 12.3497 114.339 12.7276 114.454 13.2546H115.627ZM118.883 12.295C119.624 12.295 120.112 12.8121 120.136 13.6175H117.576C117.63 12.822 118.148 12.295 118.883 12.295ZM120.147 15.3675C119.967 15.7702 119.545 15.9939 118.933 15.9939C118.123 15.9939 117.601 15.4272 117.571 14.5223V14.4577H121.369V14.06C121.369 12.3447 120.439 11.3156 118.888 11.3156C117.312 11.3156 116.333 12.4144 116.333 14.1644C116.333 15.9144 117.298 16.9733 118.899 16.9733C120.181 16.9733 121.091 16.3568 121.314 15.3675H120.147ZM122.453 16.8639H123.686V9.32202H122.453V16.8639Z"
            fill="white"
          />
          <path
            d="M14.8431 8.27271L20.7772 18.4545H8.90918L14.8431 8.27271Z"
            fill="white"
          />
          <path d="M27.6819 0V28" stroke="#5E5E5E" strokeWidth="0.636364" />
        </g>
        <defs>
          <clipPath id="clip0">
            <rect width="134.909" height="28" fill="white" />
          </clipPath>
        </defs>
      </svg>
    </a>
  )
}

export default Vercel
